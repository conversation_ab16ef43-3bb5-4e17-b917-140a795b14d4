"""
Selenium-based data collection module for JavaScript-rendered beach cam and environmental data.

This module uses Selenium with headless Chrome to extract data from pages that require
JavaScript rendering, specifically:
- meteo365.es for beach cam images and environmental data
- tideking.com for tide data

The collected data is saved to organized directories with timestamps.
"""

import json
import re
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from contextlib import contextmanager

import cv2
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager


# URLs for data collection
METEO365_URL = "https://meteo365.es/livecams/malaga.php"
TIDEKING_URL = "https://tideking.com/Spain/Andalusia/Provincia-de-Malaga/Playa-de-la-Malagueta"


@contextmanager
def get_selenium_driver(headless: bool = True, timeout: int = 30):
    """
    Context manager for Selenium WebDriver with automatic cleanup.

    Args:
        headless: Whether to run browser in headless mode
        timeout: Page load timeout in seconds

    Yields:
        WebDriver instance
    """
    options = Options()

    if headless:
        options.add_argument("--headless=new")  # Use new headless mode

    # Additional options for stability and performance
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-plugins")
    options.add_argument("--disable-web-security")
    options.add_argument("--disable-features=VizDisplayCompositor")
    options.add_argument("--remote-debugging-port=9222")

    # Realistic User-Agent
    options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    # Disable logging to reduce noise
    options.add_argument("--log-level=3")
    options.add_experimental_option("excludeSwitches", ["enable-logging"])
    options.add_experimental_option('useAutomationExtension', False)

    driver = None
    try:
        # Use webdriver-manager to automatically handle ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.set_page_load_timeout(timeout)
        driver.implicitly_wait(10)

        # Execute script to avoid detection
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        yield driver

    except Exception as e:
        print(f"Error creating WebDriver: {e}")
        if driver:
            try:
                driver.quit()
            except:
                pass
        raise
    finally:
        if driver:
            try:
                driver.quit()
            except Exception as e:
                print(f"Error closing WebDriver: {e}")


def collect_meteo365_data() -> Dict[str, Any]:
    """
    Collect beach cam image and environmental data from meteo365.es using Selenium.
    
    Returns:
        Dict containing image data, environmental measurements, and metadata
    """
    result = {
        'collection_time': datetime.now().isoformat(),
        'source': 'meteo365.es',
        'url': METEO365_URL
    }
    
    try:
        with get_selenium_driver() as driver:
            print(f"Loading {METEO365_URL}...")
            driver.get(METEO365_URL)
            
            # Wait for page to load
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Wait for specific elements to be present (indicating JS has loaded)
            try:
                WebDriverWait(driver, 30).until(
                    EC.presence_of_element_located((By.ID, "emtemp"))
                )
                print("Temperature element found, page is ready")
            except TimeoutException:
                print("Temperature element not found, continuing anyway")

            # Give additional time for JavaScript to render content
            time.sleep(10)
            
            # Extract beach cam image
            beach_cam_data = extract_beach_cam_image(driver)
            if beach_cam_data:
                result['beach_cam'] = beach_cam_data
            
            # Extract environmental data
            env_data = extract_environmental_data(driver)
            if env_data:
                result['environmental'] = env_data
                
            return result
            
    except Exception as e:
        print(f"Error collecting meteo365 data: {e}")
        result['error'] = str(e)
        return result


def extract_beach_cam_image(driver) -> Optional[Dict[str, Any]]:
    """
    Extract beach cam image from the loaded meteo365.es page using screenshot approach.
    Looks for video elements (likely blob URLs) and captures them via screenshot.

    Args:
        driver: Selenium WebDriver instance

    Returns:
        Dict containing image metadata and temp file path, or None if extraction fails
    """
    try:
        # Wait for page to be fully loaded using selenium-standard approach
        WebDriverWait(driver, 30).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        print("Page is fully loaded")

        # Handle cookie consent popup if present
        try:
            cookie_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CLASS_NAME, "fc-cta-consent"))
            )
            cookie_button.click()
            print("Clicked cookie consent button")
            time.sleep(2)  # Give time for popup to disappear
        except TimeoutException:
            print("No cookie consent popup found or already dismissed")
        except Exception as e:
            print(f"Error handling cookie popup: {e}")

        # Additional wait for any dynamic content to load after cookie consent
        time.sleep(3)
        video_selector = "video[src*='blob:']"

        target_element = None
        element_type = None

        elements = driver.find_elements(By.CSS_SELECTOR, video_selector)
        if not elements:
            raise Exception("No video element found")

        target_element = elements[0]  # Take the first video element
        element_type = "video"
        print(f"Found video element with selector: {video_selector}")

        # Scroll the element into view
        try:
            driver.execute_script("arguments[0].scrollIntoView(true);", target_element)
            time.sleep(5)  # Give time for any lazy loading
        except Exception as e:
            print(f"Could not scroll element into view: {e}")

        # Get the location and size of the element
        location = target_element.location
        size = target_element.size

        print(f"{element_type.title()} element location: {location}, size: {size}")

        # Check if we have valid dimensions
        if size['width'] <= 0 or size['height'] <= 0:
            raise Exception(f"Element has invalid dimensions: {size['width']}x{size['height']}")

        # Take a full page screenshot
        screenshot_path = 'temp_screenshot.png'
        driver.save_screenshot(screenshot_path)

        # Load the screenshot with OpenCV
        screenshot = cv2.imread(screenshot_path)
        if screenshot is None:
            raise Exception("Failed to load screenshot")

        # Extract the element region
        x = int(location['x'])
        y = int(location['y'])
        width = int(size['width'])
        height = int(size['height'])

        # Ensure coordinates are within bounds
        screenshot_height, screenshot_width = screenshot.shape[:2]
        x = max(0, min(x, screenshot_width - 1))
        y = max(0, min(y, screenshot_height - 1))
        width = min(width, screenshot_width - x)
        height = min(height, screenshot_height - y)

        if width <= 0 or height <= 0:
            raise Exception(f"Invalid crop dimensions: {width}x{height}")

        # Crop the image
        cropped_image = screenshot[y:y+height, x:x+width]

        # Save the cropped image to a temporary file
        temp_output_path = 'temp_beach_cam.jpg'
        success = cv2.imwrite(temp_output_path, cropped_image, [cv2.IMWRITE_JPEG_QUALITY, 80])

        if not success:
            raise Exception("Failed to save cropped image")

        # Clean up screenshot
        try:
            Path(screenshot_path).unlink(missing_ok=True)
        except Exception:
            pass

        # Get file size
        file_size = Path(temp_output_path).stat().st_size

        return {
            'element_type': element_type,
            'image_size_bytes': file_size,
            'content_type': 'image/jpeg',
            'extraction_method': 'selenium_screenshot',
            'crop_location': {'x': x, 'y': y, 'width': width, 'height': height},
            'temp_file_path': temp_output_path
        }

    except Exception as e:
        print(f"Error extracting beach cam image: {e}")
        # Clean up any temporary files
        try:
            Path('temp_screenshot.png').unlink(missing_ok=True)
            Path('temp_beach_cam.jpg').unlink(missing_ok=True)
        except Exception:
            pass
        return None


def extract_environmental_data(driver) -> Optional[Dict[str, Any]]:
    """
    Extract environmental data (wind, temperature, etc.) from the loaded meteo365.es page.

    Args:
        driver: Selenium WebDriver instance

    Returns:
        Dict containing environmental measurements, or None if extraction fails
    """
    try:
        env_data = {}

        # Extract current temperature from specific element
        try:
            temp_element = driver.find_element(By.ID, "emtemp")
            temp_text = temp_element.text.strip()
            if temp_text and temp_text != "nd":
                env_data['temperature_c'] = float(temp_text)
                print(f"Found temperature: {temp_text}°C")
        except Exception as e:
            print(f"Could not find temperature element: {e}")

        # Extract wind speed from specific element
        try:
            wind_element = driver.find_element(By.ID, "emwind")
            wind_text = wind_element.text.strip()
            if wind_text:
                # Wind speed is in km/h, convert to knots
                wind_kmh = float(wind_text)
                wind_knots = wind_kmh * 0.539957
                env_data['wind_speed_knots'] = wind_knots
                env_data['wind_speed_kmh'] = wind_kmh
                print(f"Found wind speed: {wind_text} km/h ({wind_knots:.1f} knots)")
        except Exception as e:
            print(f"Could not find wind speed element: {e}")

        # Extract wind direction from specific element
        try:
            wind_dir_element = driver.find_element(By.ID, "winddirection2")
            wind_dir_text = wind_dir_element.text.strip()
            if wind_dir_text:
                # Extract direction text (e.g., "WIND DIRECTION: Southeast")
                direction_match = re.search(r'WIND DIRECTION:\s*(.+)', wind_dir_text)
                if direction_match:
                    env_data['wind_direction_text'] = direction_match.group(1).strip()
                    print(f"Found wind direction: {direction_match.group(1).strip()}")
        except Exception as e:
            print(f"Could not find wind direction element: {e}")


    except Exception as e:
        print(f"Error extracting environmental data: {e}")
        return None


def collect_environmental_data_only() -> Dict[str, Any]:
    """
    Collect only environmental data (beach cam + weather) using Selenium.
    This is the main function for Selenium-based data collection.

    Returns:
        Dict containing environmental data and beach cam image
    """
    timestamp = datetime.now()

    result = {
        'collection_time': timestamp.isoformat(),
        'collection_method': 'selenium',
        'data_type': 'environmental'
    }

    # Collect meteo365 data (beach cam + environmental)
    print("Collecting environmental data from meteo365...")
    meteo365_data = collect_meteo365_data()
    if 'error' not in meteo365_data:
        if 'beach_cam' in meteo365_data:
            result['beach_cam'] = meteo365_data['beach_cam']
        if 'environmental' in meteo365_data:
            result['environmental'] = meteo365_data['environmental']
    else:
        result['meteo365_error'] = meteo365_data['error']

    return result


def save_environmental_data(data: Dict[str, Any], timestamp: datetime) -> Dict[str, str]:
    """
    Save environmental data to organized directory structure with appropriate naming.

    Args:
        data: Environmental data dictionary to save as JSON
        timestamp: Collection timestamp

    Returns:
        Dict with paths where data was saved
    """
    # Create directory structure: data/external/year/month/day
    base_dir = Path("data/external")
    date_dir = base_dir / str(timestamp.year) / f"{timestamp.month:02d}" / f"{timestamp.day:02d}"
    date_dir.mkdir(parents=True, exist_ok=True)

    # Create filename with timestamp and environ suffix
    timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")

    saved_paths = {}

    # Extract and save image data if available
    if 'beach_cam' in data and 'temp_file_path' in data['beach_cam']:
        temp_file_path = data['beach_cam']['temp_file_path']

        # Determine file extension based on content type
        content_type = data['beach_cam'].get('content_type', 'image/jpeg')
        if 'webp' in content_type:
            image_filename = f"{timestamp_str}_environ.webp"
        elif 'png' in content_type:
            image_filename = f"{timestamp_str}_environ.png"
        else:
            image_filename = f"{timestamp_str}_environ.jpg"

        image_path = date_dir / image_filename

        # Move the temp file to the final location
        try:
            import shutil
            shutil.move(temp_file_path, image_path)
            saved_paths['image_path'] = str(image_path)
            saved_paths['image_filename'] = image_filename

            # Update the data with the image filename and remove temp file path
            data['beach_cam']['image_saved'] = image_filename
            del data['beach_cam']['temp_file_path']
        except Exception as e:
            print(f"Error moving temp file: {e}")
            # Try to clean up temp file
            try:
                Path(temp_file_path).unlink(missing_ok=True)
            except Exception:
                pass

    # Save JSON data with environ suffix
    json_path = date_dir / f"{timestamp_str}_environ.json"
    with open(json_path, 'w') as f:
        json.dump(data, f, indent=2)
    saved_paths['json_path'] = str(json_path)

    return saved_paths


if __name__ == "__main__":
    """
    Example usage: collect environmental data and save to files.
    """
    print("Starting Selenium-based environmental data collection...")

    # Collect environmental data
    data = collect_environmental_data_only()

    # Save data
    timestamp = datetime.now()
    saved_paths = save_environmental_data(data, timestamp)

    print("Data collection completed!")
    print(f"JSON saved to: {saved_paths.get('json_path', 'N/A')}")
    if 'image_path' in saved_paths:
        print(f"Image saved to: {saved_paths['image_path']}")

    # Print summary
    print("\nCollection Summary:")
    print(f"- Collection time: {data['collection_time']}")
    print(f"- Beach cam: {'✓' if 'beach_cam' in data else '✗'}")
    print(f"- Environmental data: {'✓' if 'environmental' in data else '✗'}")

    if 'meteo365_error' in data:
        print(f"- Meteo365 error: {data['meteo365_error']}")
