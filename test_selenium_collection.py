#!/usr/bin/env python3
"""
Test script for Selenium-based data collection.

This script demonstrates how to use the selenium_data_collection module
to extract beach cam images and environmental data from JavaScript-rendered pages.
"""

import sys
from pathlib import Path

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from surf_malaga.selenium_data_collection import (
    collect_environmental_data_only,
    save_environmental_data,
)
from datetime import datetime


def test_environmental_collection():
    """Test collecting environmental data and saving to files."""
    print("\n" + "=" * 60)
    print("Testing environmental data collection and saving...")
    print("=" * 60)

    try:
        # Collect environmental data
        data = collect_environmental_data_only()

        print(f"Collection time: {data.get('collection_time', 'N/A')}")
        print(f"Collection method: {data.get('collection_method', 'N/A')}")
        print(f"Data type: {data.get('data_type', 'N/A')}")

        # Save data
        timestamp = datetime.now()
        saved_paths = save_environmental_data(data, timestamp)

        print(f"✅ Data saved successfully:")
        print(f"   - JSON: {saved_paths.get('json_path', 'N/A')}")
        if 'image_path' in saved_paths:
            print(f"   - Image: {saved_paths['image_path']}")

        # Print summary
        print(f"\n📊 Collection Summary:")
        print(f"   - Beach cam: {'✅' if 'beach_cam' in data else '❌'}")
        print(f"   - Environmental data: {'✅' if 'environmental' in data else '❌'}")

        if 'meteo365_error' in data:
            print(f"   - Meteo365 error: {data['meteo365_error']}")

        return True

    except Exception as e:
        print(f"❌ Exception during environmental collection: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Selenium Environmental Data Collection Tests")
    print("This will test extracting beach cam images and environmental data")
    print("from JavaScript-rendered pages using headless Chrome.\n")

    # Test individual components
    environmental_success = test_environmental_collection()

    # Final summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Environmental collection & save: {'✅ PASS' if environmental_success else '❌ FAIL'}")

    if environmental_success:
        print("\n🎉 Environmental data collection is working!")
        print("Check the data/external/ directory for saved files with '_environ' suffix.")
        print("Note: Tide data collection uses the basic parsing approach in data_collection.py")
    else:
        print("\n⚠️  All tests failed. Check your internet connection and try again.")
        print("You may need to inspect the actual page structure and update the selectors.")


if __name__ == "__main__":
    main()
