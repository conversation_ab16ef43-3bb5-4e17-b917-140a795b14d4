#!/usr/bin/env python3
"""
Debug script to inspect page content and find the right selectors for data extraction.
"""

import sys
from pathlib import Path
import re

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from surf_malaga.selenium_data_collection import get_selenium_driver


def debug_meteo365_page():
    """Debug the meteo365 page to find images and environmental data."""
    print("🔍 Debugging meteo365.es page content...")
    
    try:
        with get_selenium_driver(headless=True, timeout=60) as driver:  # Use headless for automated debugging
            print("Loading https://meteo365.es/livecams/malaga.php...")
            driver.get("https://meteo365.es/livecams/malaga.php")
            
            # Wait for page to load
            import time
            time.sleep(10)  # Give more time for content to load
            
            print(f"Page title: {driver.title}")
            print(f"Page URL: {driver.current_url}")
            
            # Find all images
            print("\n🖼️ Looking for images...")
            images = driver.find_elements("tag name", "img")
            print(f"Found {len(images)} images:")
            
            for i, img in enumerate(images[:10]):  # Show first 10 images
                src = img.get_attribute('src')
                alt = img.get_attribute('alt')
                class_name = img.get_attribute('class')
                id_name = img.get_attribute('id')
                
                print(f"  {i+1}. src: {src}")
                print(f"     alt: {alt}")
                print(f"     class: {class_name}")
                print(f"     id: {id_name}")
                print()
            
            # Look for video elements (might be a video stream)
            print("\n📹 Looking for video elements...")
            videos = driver.find_elements("tag name", "video")
            print(f"Found {len(videos)} videos:")
            
            for i, video in enumerate(videos):
                src = video.get_attribute('src')
                poster = video.get_attribute('poster')
                class_name = video.get_attribute('class')
                id_name = video.get_attribute('id')
                
                print(f"  {i+1}. src: {src}")
                print(f"     poster: {poster}")
                print(f"     class: {class_name}")
                print(f"     id: {id_name}")
                print()
            
            # Look for iframes (might contain the webcam)
            print("\n🖼️ Looking for iframes...")
            iframes = driver.find_elements("tag name", "iframe")
            print(f"Found {len(iframes)} iframes:")
            
            for i, iframe in enumerate(iframes):
                src = iframe.get_attribute('src')
                class_name = iframe.get_attribute('class')
                id_name = iframe.get_attribute('id')
                
                print(f"  {i+1}. src: {src}")
                print(f"     class: {class_name}")
                print(f"     id: {id_name}")
                print()
            
            # Look for elements containing weather/temperature data
            print("\n🌡️ Looking for temperature/weather data...")
            
            # Search for text containing temperature patterns
            page_text = driver.page_source
            temp_patterns = [
                r'(\d+)\s*°[CF]?',
                r'temperatura[:\s]*(\d+)',
                r'temp[:\s]*(\d+)',
                r'(\d+)\s*grados'
            ]
            
            for pattern in temp_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    print(f"  Temperature pattern '{pattern}' found: {matches[:5]}")  # Show first 5 matches
            
            # Look for wind data
            print("\n💨 Looking for wind data...")
            wind_patterns = [
                r'(\d+)\s*(km/h|mph|knots|kts)',
                r'viento[:\s]*(\d+)',
                r'wind[:\s]*(\d+)'
            ]
            
            for pattern in wind_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    print(f"  Wind pattern '{pattern}' found: {matches[:5]}")
            
            # Look for elements with specific classes/IDs that might contain data
            print("\n🔍 Looking for elements with weather-related classes/IDs...")
            weather_selectors = [
                "*[class*='temp']",
                "*[class*='weather']",
                "*[class*='wind']",
                "*[class*='clima']",
                "*[id*='temp']",
                "*[id*='weather']",
                "*[id*='wind']",
                "*[id*='clima']"
            ]
            
            for selector in weather_selectors:
                try:
                    elements = driver.find_elements("css selector", selector)
                    if elements:
                        print(f"  Selector '{selector}' found {len(elements)} elements:")
                        for elem in elements[:3]:  # Show first 3
                            text = elem.text.strip()
                            if text:
                                print(f"    - {text[:100]}...")  # First 100 chars
                except Exception as e:
                    print(f"    Error with selector '{selector}': {e}")
            
            # Save page source for manual inspection
            with open("debug_meteo365_page_source.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print(f"\n💾 Page source saved to debug_meteo365_page_source.html")
            
            print("\n✅ Debug complete. Check the saved HTML file.")
            
    except Exception as e:
        print(f"❌ Error during debugging: {e}")


def debug_tideking_page():
    """Debug the tideking page to understand its structure."""
    print("\n🔍 Debugging tideking.com page content...")
    
    try:
        with get_selenium_driver(headless=True, timeout=60) as driver:
            print("Loading https://tideking.com/Spain/Andalusia/Provincia-de-Malaga/Playa-de-la-Malagueta...")
            driver.get("https://tideking.com/Spain/Andalusia/Provincia-de-Malaga/Playa-de-la-Malagueta")
            
            # Wait for page to load
            import time
            time.sleep(10)
            
            print(f"Page title: {driver.title}")
            print(f"Page URL: {driver.current_url}")
            
            # Look for tide table
            print("\n🌊 Looking for tide tables...")
            tables = driver.find_elements("tag name", "table")
            print(f"Found {len(tables)} tables:")
            
            for i, table in enumerate(tables):
                rows = table.find_elements("tag name", "tr")
                print(f"  Table {i+1}: {len(rows)} rows")
                
                # Show first few rows
                for j, row in enumerate(rows[:3]):
                    cells = row.find_elements("tag name", "td") + row.find_elements("tag name", "th")
                    cell_texts = [cell.text.strip() for cell in cells]
                    print(f"    Row {j+1}: {cell_texts}")
            
            # Save page source
            with open("debug_tideking_page_source.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print(f"\n💾 Page source saved to debug_tideking_page_source.html")
            
            print("\n✅ Debug complete. Check the saved HTML file.")
            
    except Exception as e:
        print(f"❌ Error during debugging: {e}")


def main():
    """Run debugging for both sites."""
    print("🚀 Starting Page Content Debugging")
    print("This will inspect the actual page content to find the right selectors.")
    print("=" * 60)

    # Debug meteo365 only for now
    debug_meteo365_page()

    print("\n🎉 Debugging complete!")
    print("Check the saved HTML files and use the information to improve selectors.")


if __name__ == "__main__":
    main()
