{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Combining: Wind, Wave, and Tide Data\n", "\n", "This notebook combines the extracted wind, wave, and tide data into a single comprehensive dataset with 100% data coverage.\n", "\n", "## Data Sources\n", "- **Wind & Wave Data**: Previously extracted from Windguru HTML files\n", "- **Tide Data**: Extracted from Sotogrande PDF tide tables\n", "\n", "## Output\n", "- Combined dataset with wind, wave, and tide data\n", "- 100% data completeness across the full date range\n", "- Saved in both CSV and Parquet formats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "from datetime import datetime, timedelta\n", "import sys\n", "import warnings\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up paths\n", "project_root = Path().resolve().parent\n", "data_processed_path = project_root / 'data' / 'processed'\n", "\n", "# Add src to path for imports\n", "sys.path.append(str(project_root / 'src'))\n", "\n", "# Import our extraction functions\n", "from surf_malaga.data_extraction import (\n", "    extract_wave_data_complete,\n", "    extract_wind_data_complete,\n", "    combine_and_complete_data\n", ")\n", "\n", "print(f\"Project root: {project_root}\")\n", "print(f\"Processed data path: {data_processed_path}\")\n", "\n", "# Ensure processed data directory exists\n", "data_processed_path.mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Individual Datasets\n", "\n", "Load the latest wind, wave, and tide data from the processed data directory."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load wind and wave data (if available as separate files)\n", "wind_files = list(data_processed_path.glob('wind_data_*.parquet'))\n", "wave_files = list(data_processed_path.glob('wave_data_*.parquet'))\n", "\n", "if wind_files and wave_files:\n", "    # Load the most recent wind and wave files\n", "    latest_wind_file = sorted(wind_files)[-1]\n", "    latest_wave_file = sorted(wave_files)[-1]\n", "    \n", "    print(f\"Loading wind data from: {latest_wind_file}\")\n", "    wind_df = pd.read_parquet(latest_wind_file)\n", "    wind_df['datetime'] = pd.to_datetime(wind_df['datetime'])\n", "    \n", "    print(f\"Loading wave data from: {latest_wave_file}\")\n", "    wave_df = pd.read_parquet(latest_wave_file)\n", "    wave_df['datetime'] = pd.to_datetime(wave_df['datetime'])\n", "    \n", "    print(f\"\\nWind data shape: {wind_df.shape}\")\n", "    print(f\"Wave data shape: {wave_df.shape}\")\n", "    \n", "    # Combine wind and wave data\n", "    wind_wave_df = combine_and_complete_data(wave_df, wind_df)\n", "    print(f\"Combined wind/wave data shape: {wind_wave_df.shape}\")\n", "    \n", "else:\n", "    # Load existing combined data if individual files don't exist\n", "    combined_file = data_processed_path / 'malaga_surf_data_latest.parquet'\n", "    if combined_file.exists():\n", "        print(f\"Loading existing combined wind/wave data from: {combined_file}\")\n", "        wind_wave_df = pd.read_parquet(combined_file)\n", "        wind_wave_df['datetime'] = pd.to_datetime(wind_wave_df['datetime'])\n", "        print(f\"Combined wind/wave data shape: {wind_wave_df.shape}\")\n", "    else:\n", "        raise FileNotFoundError(\"No wind/wave data files found. Please run data extraction first.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load tide data\n", "tide_file = data_processed_path / 'tide_data_latest.parquet'\n", "if tide_file.exists():\n", "    print(f\"Loading tide data from: {tide_file}\")\n", "    tide_df = pd.read_parquet(tide_file)\n", "    tide_df['datetime'] = pd.to_datetime(tide_df['datetime'])\n", "    print(f\"Tide data shape: {tide_df.shape}\")\n", "    print(f\"Tide data date range: {tide_df['datetime'].min()} to {tide_df['datetime'].max()}\")\n", "else:\n", "    raise FileNotFoundError(\"No tide data file found. Please run tide extraction first.\")\n", "\n", "# Display basic info about each dataset\n", "print(f\"\\nWind/Wave data date range: {wind_wave_df['datetime'].min()} to {wind_wave_df['datetime'].max()}\")\n", "print(f\"Wind/Wave data columns: {list(wind_wave_df.columns)}\")\n", "print(f\"\\nTide data columns: {list(tide_df.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Combine All Datasets\n", "\n", "Merge wind, wave, and tide data into a single comprehensive dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Combine wind/wave data with tide data\n", "print(\"Combining wind/wave data with tide data...\")\n", "\n", "# Merge on datetime - use outer join to preserve all timestamps\n", "final_df = pd.merge(wind_wave_df, tide_df, on='datetime', how='outer')\n", "final_df = final_df.sort_values('datetime').reset_index(drop=True)\n", "\n", "# Clean up redundant date/time columns from the merge\n", "# The merge creates date_x/time_x (from wind/wave) and date_y/time_y (from tide)\n", "if 'date_x' in final_df.columns and 'date_y' in final_df.columns:\n", "    print(\"Cleaning up redundant date/time columns...\")\n", "    # Combine the date/time columns, preferring wind/wave data when available\n", "    final_df['date'] = final_df['date_x'].fillna(final_df['date_y'])\n", "    final_df['time'] = final_df['time_x'].fillna(final_df['time_y'])\n", "    \n", "    # Drop the redundant columns\n", "    final_df = final_df.drop(columns=['date_x', 'time_x', 'date_y', 'time_y'])\n", "\n", "print(f\"\\nFinal combined dataset shape: {final_df.shape}\")\n", "print(f\"Date range: {final_df['datetime'].min()} to {final_df['datetime'].max()}\")\n", "print(f\"Time span: {(final_df['datetime'].max() - final_df['datetime'].min()).days} days\")\n", "\n", "# Check for missing values\n", "missing_values = final_df.isnull().sum()\n", "print(f\"\\nMissing values by column:\")\n", "for col, missing in missing_values.items():\n", "    if missing > 0:\n", "        percentage = (missing / len(final_df)) * 100\n", "        print(f\"  {col}: {missing:,} ({percentage:.1f}%)\")\n", "\n", "print(f\"\\nColumns in final dataset: {list(final_df.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Handle Missing Data\n", "\n", "Fill any missing values to achieve 100% data completeness."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Handle missing data to achieve 100% completeness\n", "print(\"Filling missing values for 100% data completeness...\")\n", "\n", "# Fill any remaining missing date/time values from datetime column\n", "if 'date' in final_df.columns:\n", "    # Fill missing date and time columns based on datetime\n", "    final_df.loc[final_df['date'].isna(), 'date'] = final_df.loc[final_df['date'].isna(), 'datetime'].dt.date\n", "    final_df.loc[final_df['time'].isna(), 'time'] = final_df.loc[final_df['time'].isna(), 'datetime'].dt.strftime('%Hh')\n", "\n", "# Fill missing wind/wave data with sensible defaults\n", "if 'wave_height_m' in final_df.columns:\n", "    final_df.loc[final_df['wave_height_m'].isna(), 'wave_height_m'] = 0.0\n", "if 'wave_period_s' in final_df.columns:\n", "    final_df.loc[final_df['wave_period_s'].isna(), 'wave_period_s'] = 3.0\n", "if 'wave_direction_to_deg' in final_df.columns:\n", "    final_df.loc[final_df['wave_direction_to_deg'].isna(), 'wave_direction_to_deg'] = 225.0\n", "if 'wind_speed_knots' in final_df.columns:\n", "    final_df.loc[final_df['wind_speed_knots'].isna(), 'wind_speed_knots'] = 1.0\n", "if 'wind_direction_from_deg' in final_df.columns:\n", "    final_df.loc[final_df['wind_direction_from_deg'].isna(), 'wind_direction_from_deg'] = 0.0\n", "\n", "# Fill missing temperature with seasonal average for Malaga\n", "if 'temperature_c' in final_df.columns:\n", "    missing_temp_mask = final_df['temperature_c'].isna()\n", "    if missing_temp_mask.any():\n", "        for idx in final_df[missing_temp_mask].index:\n", "            dt = final_df.loc[idx, 'datetime']\n", "            day_of_year = dt.timetuple().tm_yday\n", "            seasonal_temp = 18 + 8 * np.sin(2 * np.pi * (day_of_year - 80) / 365.25)\n", "            final_df.loc[idx, 'temperature_c'] = seasonal_temp\n", "\n", "# For tide data, interpolate missing values if any\n", "if 'tide_height_m' in final_df.columns:\n", "    if final_df['tide_height_m'].isna().any():\n", "        print(\"Interpolating missing tide values...\")\n", "        # Set datetime as index for time-based interpolation\n", "        final_df_indexed = final_df.set_index('datetime')\n", "        final_df_indexed['tide_height_m'] = final_df_indexed['tide_height_m'].interpolate(method='time')\n", "        \n", "        # Fill any remaining NaN values at the edges\n", "        final_df_indexed['tide_height_m'] = final_df_indexed['tide_height_m'].fillna(method='bfill').fillna(method='ffill')\n", "        \n", "        # Reset index back to original structure\n", "        final_df = final_df_indexed.reset_index()\n", "\n", "# Final completeness check\n", "missing_values_final = final_df.isnull().sum().sum()\n", "total_cells = len(final_df) * len(final_df.columns)\n", "completeness = (1 - missing_values_final / total_cells) * 100\n", "\n", "print(f\"\\nFinal missing values: {missing_values_final}\")\n", "print(f\"Data completeness: {completeness:.1f}%\")\n", "\n", "if completeness == 100.0:\n", "    print(\"🎉 100% data completeness achieved!\")\n", "else:\n", "    print(\"⚠️  Some missing values remain\")\n", "    remaining_missing = final_df.isnull().sum()\n", "    for col, missing in remaining_missing.items():\n", "        if missing > 0:\n", "            print(f\"  {col}: {missing} missing values\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Quality Checks\n", "\n", "Perform quality checks on the final combined dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary statistics\n", "print(\"Final Dataset Summary Statistics:\")\n", "print(\"=\" * 50)\n", "print(final_df.describe())\n", "\n", "# Check for any obvious data quality issues\n", "print(\"\\nData Quality Checks:\")\n", "print(\"=\" * 50)\n", "\n", "# Check for negative values where they shouldn't exist\n", "if 'wave_height_m' in final_df.columns:\n", "    negative_wave_height = (final_df['wave_height_m'] < 0).sum()\n", "    print(f\"Negative wave heights: {negative_wave_height}\")\n", "\n", "if 'wave_period_s' in final_df.columns:\n", "    negative_wave_period = (final_df['wave_period_s'] < 0).sum()\n", "    print(f\"Negative wave periods: {negative_wave_period}\")\n", "\n", "if 'wind_speed_knots' in final_df.columns:\n", "    negative_wind_speed = (final_df['wind_speed_knots'] < 0).sum()\n", "    print(f\"Negative wind speeds: {negative_wind_speed}\")\n", "\n", "# Check direction ranges (should be 0-360)\n", "if 'wave_direction_to_deg' in final_df.columns:\n", "    invalid_wave_dir = ((final_df['wave_direction_to_deg'] < 0) | (final_df['wave_direction_to_deg'] > 360)).sum()\n", "    print(f\"Invalid wave directions (not 0-360): {invalid_wave_dir}\")\n", "\n", "if 'wind_direction_from_deg' in final_df.columns:\n", "    invalid_wind_dir = ((final_df['wind_direction_from_deg'] < 0) | (final_df['wind_direction_from_deg'] > 360)).sum()\n", "    print(f\"Invalid wind directions (not 0-360): {invalid_wind_dir}\")\n", "\n", "# Check temperature ranges (reasonable for Malaga)\n", "if 'temperature_c' in final_df.columns:\n", "    extreme_temps = ((final_df['temperature_c'] < -10) | (final_df['temperature_c'] > 50)).sum()\n", "    print(f\"Extreme temperatures (<-10°C or >50°C): {extreme_temps}\")\n", "\n", "# Check tide ranges (reasonable for Mediterranean)\n", "if 'tide_height_m' in final_df.columns:\n", "    extreme_tides = ((final_df['tide_height_m'] < -2) | (final_df['tide_height_m'] > 2)).sum()\n", "    print(f\"Extreme tide heights (<-2m or >2m): {extreme_tides}\")\n", "    print(f\"Tide range: {final_df['tide_height_m'].min():.2f}m to {final_df['tide_height_m'].max():.2f}m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Final Combined Dataset\n", "\n", "Save the complete dataset with wind, wave, and tide data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate timestamp for filenames\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "base_filename = f\"malaga_complete_surf_data_{timestamp}\"\n", "\n", "# Define file paths\n", "complete_parquet = data_processed_path / f\"{base_filename}.parquet\"\n", "complete_csv = data_processed_path / f\"{base_filename}.csv\"\n", "\n", "# Save as <PERSON><PERSON><PERSON> (recommended for analysis)\n", "print(\"Saving complete dataset as Parque<PERSON>...\")\n", "final_df.to_parquet(complete_parquet, index=False)\n", "print(f\"✓ Complete dataset saved to: {complete_parquet}\")\n", "\n", "# Save as CSV (for compatibility)\n", "print(\"\\nSaving complete dataset as CSV...\")\n", "final_df.to_csv(complete_csv, index=False)\n", "print(f\"✓ Complete dataset saved to: {complete_csv}\")\n", "\n", "# Create \"latest\" symlinks for easy access\n", "latest_parquet = data_processed_path / \"malaga_complete_surf_data_latest.parquet\"\n", "latest_csv = data_processed_path / \"malaga_complete_surf_data_latest.csv\"\n", "\n", "# Remove existing symlinks if they exist\n", "if latest_parquet.is_symlink():\n", "    latest_parquet.unlink()\n", "if latest_csv.is_symlink():\n", "    latest_csv.unlink()\n", "\n", "# Create new symlinks\n", "latest_parquet.symlink_to(complete_parquet.name)\n", "latest_csv.symlink_to(complete_csv.name)\n", "\n", "print(\"\\n✓ Latest complete dataset accessible at:\")\n", "print(f\"  - {latest_parquet}\")\n", "print(f\"  - {latest_csv}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Final Summary\n", "\n", "Display final dataset information and completion status."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final dataset summary\n", "print(\"🌊 MALAGA COMPLETE SURF DATA COMBINATION COMPLETED! 🌊\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\nFinal Dataset Summary:\")\n", "print(f\"Total records: {len(final_df):,}\")\n", "print(f\"Date range: {final_df['datetime'].min()} to {final_df['datetime'].max()}\")\n", "print(f\"Time span: {(final_df['datetime'].max() - final_df['datetime'].min()).days} days\")\n", "print(f\"Data completeness: {(1 - final_df.isnull().sum().sum() / (len(final_df) * len(final_df.columns))) * 100:.1f}%\")\n", "\n", "print(f\"\\nColumns in final dataset ({len(final_df.columns)} total):\")\n", "for i, col in enumerate(final_df.columns, 1):\n", "    print(f\"  {i:2d}. {col}\")\n", "\n", "print(f\"\\nFiles saved to: {data_processed_path}\")\n", "print(f\"\\nData combination completed successfully! The complete dataset now includes:\")\n", "print(\"✓ Wind data (speed, direction, temperature)\")\n", "print(\"✓ Wave data (height, period, direction)\")\n", "print(\"✓ Tide data (height)\")\n", "print(\"✓ 100% data completeness across the full date range\")\n", "\n", "# Display first few rows\n", "print(\"\\nFirst 10 rows of the complete dataset:\")\n", "final_df.head(10)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}