#!/usr/bin/env python3
"""
Test script for the updated screenshot-based beach cam image extraction.
"""

import sys
from pathlib import Path

# Add src to path so we can import our module
sys.path.insert(0, str(Path(__file__).parent / "src"))

from surf_malaga.selenium_data_collection import collect_environmental_data_only, save_environmental_data
from datetime import datetime

def main():
    """Test the screenshot-based image extraction."""
    print("Testing screenshot-based beach cam image extraction...")
    
    try:
        # Collect data using the updated method
        data = collect_environmental_data_only()
        
        # Print results
        print("\n=== Collection Results ===")
        print(f"Collection time: {data.get('collection_time', 'N/A')}")
        print(f"Collection method: {data.get('collection_method', 'N/A')}")
        
        # Check beach cam data
        if 'beach_cam' in data:
            beach_cam = data['beach_cam']
            print(f"\n=== Beach Cam Data ===")
            print(f"Image URL: {beach_cam.get('image_url', 'N/A')}")
            print(f"Image size: {beach_cam.get('image_size_bytes', 0)} bytes")
            print(f"Content type: {beach_cam.get('content_type', 'N/A')}")
            print(f"Extraction method: {beach_cam.get('extraction_method', 'N/A')}")
            
            if 'crop_location' in beach_cam:
                crop = beach_cam['crop_location']
                print(f"Crop location: x={crop['x']}, y={crop['y']}, w={crop['width']}, h={crop['height']}")
            
            if '_image_data' in beach_cam:
                print(f"Image data successfully extracted: {len(beach_cam['_image_data'])} bytes")
            else:
                print("No image data extracted")
                
            if 'error' in beach_cam:
                print(f"Error: {beach_cam['error']}")
        else:
            print("No beach cam data found")
        
        # Check environmental data
        if 'environmental' in data:
            env = data['environmental']
            print(f"\n=== Environmental Data ===")
            for key, value in env.items():
                print(f"{key}: {value}")
        else:
            print("No environmental data found")
        
        # Save the data
        if 'beach_cam' in data or 'environmental' in data:
            timestamp = datetime.now()
            saved_paths = save_environmental_data(data, timestamp)
            print(f"\n=== Saved Files ===")
            for key, path in saved_paths.items():
                print(f"{key}: {path}")
        
        return True
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
